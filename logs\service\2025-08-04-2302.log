Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-08-04T23:02:36.004365500+08:00" level=info msg="Start initial configuration in progress"
time="2025-08-04T23:02:36.011864100+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-08-04T23:02:36.011864100+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-08-04T23:02:36.012374600+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-08-04T23:02:36.017032300+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7482"
time="2025-08-04T23:02:36.017692500+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-08-04T23:02:36.224258600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118239"
time="2025-08-04T23:02:36.229761600+08:00" level=info msg="Initial configuration complete, total time: 222ms"
time="2025-08-04T23:02:36.266833700+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-08-04T23:02:36.837410400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-04T23:02:36.841240000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-04T23:02:36.841240000+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-04T23:02:37.096785800+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-04T23:02:37.297294600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: no ip address"
time="2025-08-04T23:02:37.342900200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T23:02:37.342900200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T23:02:37.342900200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T23:02:37.342900200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
