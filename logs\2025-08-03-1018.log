2025-08-03 10:18:25 INFO - [Config] clash_core配置验证通过: Some("verge-mihomo")
2025-08-03 10:18:25 INFO - [Setup] 初始化资源...
2025-08-03 10:18:25 INFO - [Setup] 初始化完成，继续执行
2025-08-03 10:18:25 INFO - [System] 应用就绪或恢复
2025-08-03 10:18:25 INFO - [Config] 生成运行时配置成功
2025-08-03 10:18:25 INFO - [Config] 开始验证配置
2025-08-03 10:18:25 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 10:18:25 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 10:18:25 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 10:18:25 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 10:18:27 INFO - [Config] -------- 验证结果 --------
2025-08-03 10:18:27 INFO - [Config] 验证成功
2025-08-03 10:18:27 INFO - [Config] -------- 验证结束 --------
2025-08-03 10:18:27 INFO - [Config] 配置验证成功
2025-08-03 10:18:27 INFO - [Setup] 清理冗余的Profile文件...
2025-08-03 10:18:27 INFO - Profile 文件清理完成: 总文件数=87, 删除文件数=0, 失败数=0
2025-08-03 10:18:27 INFO - [Setup] 启动时Profile文件清理完成
2025-08-03 10:18:27 INFO - [Core] 开始清理多余的 mihomo 进程
2025-08-03 10:18:27 INFO - [Core] 尝试终止进程: verge-mihomo.exe (PID: 30768)
2025-08-03 10:18:27 WARN - [Core] 无法终止进程: verge-mihomo.exe (PID: 30768)
2025-08-03 10:18:27 INFO - [Service] 开始检查服务是否正在运行
2025-08-03 10:18:27 INFO - [Service] 开始检查服务状态 (IPC)
2025-08-03 10:18:27 INFO - [Service] 正在连接服务 (Windows)...
2025-08-03 10:18:27 INFO - [Service] 服务连接成功 (Windows)
2025-08-03 10:18:27 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-08-03 10:18:27 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-08-03 10:18:27 INFO - [Service] 服务正在运行
2025-08-03 10:18:27 INFO - [Core] 服务当前可用或看似可用，尝试通过服务模式启动/重装
2025-08-03 10:18:27 INFO - [Service] 开始检查服务是否需要重装
2025-08-03 10:18:27 INFO - [Service] 开始检查服务版本 (IPC)
2025-08-03 10:18:27 INFO - [Service] 正在连接服务 (Windows)...
2025-08-03 10:18:27 INFO - [Service] 服务连接成功 (Windows)
2025-08-03 10:18:27 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-08-03 10:18:27 INFO - [Service] 获取到服务版本: 1.1.0
2025-08-03 10:18:27 INFO - 服务版本检测：当前=1.1.0, 要求=1.1.0
2025-08-03 10:18:27 INFO - 服务版本匹配，无需重装
2025-08-03 10:18:27 INFO - 正在尝试通过服务启动核心
2025-08-03 10:18:27 INFO - [Service] 开始检查服务版本 (IPC)
2025-08-03 10:18:27 INFO - [Service] 正在连接服务 (Windows)...
2025-08-03 10:18:27 INFO - [Service] 服务连接成功 (Windows)
2025-08-03 10:18:27 INFO - [Service] IPC请求完成: 命令=GetVersion, 成功=true
2025-08-03 10:18:27 INFO - [Service] 获取到服务版本: 1.1.0
2025-08-03 10:18:27 INFO - 检测到服务版本: 1.1.0, 要求版本: 1.1.0
2025-08-03 10:18:27 INFO - 服务版本匹配
2025-08-03 10:18:27 INFO - [Service] 开始检查服务是否正在运行
2025-08-03 10:18:27 INFO - [Service] 开始检查服务状态 (IPC)
2025-08-03 10:18:27 INFO - [Service] 正在连接服务 (Windows)...
2025-08-03 10:18:27 INFO - [Service] 服务连接成功 (Windows)
2025-08-03 10:18:27 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-08-03 10:18:27 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-08-03 10:18:27 INFO - [Service] 服务正在运行
2025-08-03 10:18:27 INFO - 服务已在运行且版本匹配，尝试使用
2025-08-03 10:18:27 INFO - 尝试使用现有服务启动核心 (IPC)
2025-08-03 10:18:27 INFO - [Service] 正在连接服务 (Windows)...
2025-08-03 10:18:27 INFO - [Service] 服务连接成功 (Windows)
2025-08-03 10:18:28 INFO - [Service] IPC请求完成: 命令=StartClash, 成功=true
2025-08-03 10:18:28 INFO - [Service] 服务成功启动核心
2025-08-03 10:18:28 INFO - [Core] 服务模式成功启动核心
2025-08-03 10:18:28 INFO - [Tray] 创建系统托盘...
2025-08-03 10:18:28 INFO - 正在从AppHandle创建系统托盘
2025-08-03 10:18:28 INFO - 系统托盘创建成功
2025-08-03 10:18:28 INFO - [Tray] 系统托盘创建成功
2025-08-03 10:18:28 INFO - 已启用事件驱动代理守卫
2025-08-03 10:18:28 INFO - [Window] 开始创建/显示主窗口, is_show=true
2025-08-03 10:18:28 INFO - 事件驱动代理管理器启动
2025-08-03 10:18:28 INFO - 初始化代理状态
2025-08-03 10:18:28 INFO - 代理状态初始化完成: sys=false, pac=false
2025-08-03 10:18:28 INFO - [Timer] Initializing timer...
2025-08-03 10:18:28 INFO - [Timer] Refreshing 7 timer tasks
2025-08-03 10:18:28 INFO - [Timer] Adding task: uid=RO6hk7oF7wdt, id=4, interval=1440min
2025-08-03 10:18:28 INFO - [Timer] Adding task: uid=RqkaNnsWj6RG, id=1, interval=1440min
2025-08-03 10:18:28 INFO - [Timer] Adding task: uid=RqUQUTUdf5hK, id=7, interval=1440min
2025-08-03 10:18:28 INFO - [Timer] Adding task: uid=Rx9LL6kjRU3B, id=6, interval=360min
2025-08-03 10:18:28 INFO - [Timer] Adding task: uid=RJRGh1X9IWCo, id=5, interval=1440min
2025-08-03 10:18:28 INFO - [Timer] Adding task: uid=RKzTK4d5rjIF, id=2, interval=1440min
2025-08-03 10:18:28 INFO - [Timer] Adding task: uid=RFHm8frTGpzz, id=3, interval=1440min
2025-08-03 10:18:28 INFO - [Timer] 已注册的定时任务数量: 7
2025-08-03 10:18:28 INFO - [Timer] 注册了定时任务 - uid=RO6hk7oF7wdt, interval=1440min, task_id=4
2025-08-03 10:18:28 INFO - [Timer] 注册了定时任务 - uid=RqUQUTUdf5hK, interval=1440min, task_id=7
2025-08-03 10:18:28 INFO - [Timer] 注册了定时任务 - uid=RFHm8frTGpzz, interval=1440min, task_id=3
2025-08-03 10:18:28 INFO - [Timer] 注册了定时任务 - uid=RqkaNnsWj6RG, interval=1440min, task_id=1
2025-08-03 10:18:28 INFO - [Timer] 注册了定时任务 - uid=Rx9LL6kjRU3B, interval=360min, task_id=6
2025-08-03 10:18:28 INFO - [Timer] 注册了定时任务 - uid=RKzTK4d5rjIF, interval=1440min, task_id=2
2025-08-03 10:18:28 INFO - [Timer] 注册了定时任务 - uid=RJRGh1X9IWCo, interval=1440min, task_id=5
2025-08-03 10:18:28 INFO - [Timer] 需要立即更新的配置: uid=RKzTK4d5rjIF
2025-08-03 10:18:28 INFO - [Timer] 需要立即更新的配置: uid=RqUQUTUdf5hK
2025-08-03 10:18:28 INFO - [Timer] 需要立即更新的配置: uid=RqkaNnsWj6RG
2025-08-03 10:18:28 INFO - [Timer] 需要立即更新的配置: uid=Rx9LL6kjRU3B
2025-08-03 10:18:28 INFO - [Timer] 需要立即更新的配置: uid=RFHm8frTGpzz
2025-08-03 10:18:28 INFO - [Timer] 需要立即更新的配置: uid=RO6hk7oF7wdt
2025-08-03 10:18:28 INFO - [Timer] 需要立即更新的配置: uid=RJRGh1X9IWCo
2025-08-03 10:18:28 INFO - [Timer] 需要立即更新的配置数量: 7
2025-08-03 10:18:28 INFO - [Timer] 立即执行任务: uid=RKzTK4d5rjIF
2025-08-03 10:18:28 INFO - [Timer] 立即执行任务: uid=RqUQUTUdf5hK
2025-08-03 10:18:28 INFO - [Timer] 立即执行任务: uid=RqkaNnsWj6RG
2025-08-03 10:18:28 INFO - [Timer] 立即执行任务: uid=Rx9LL6kjRU3B
2025-08-03 10:18:28 INFO - [Timer] 立即执行任务: uid=RFHm8frTGpzz
2025-08-03 10:18:28 INFO - [Timer] 立即执行任务: uid=RO6hk7oF7wdt
2025-08-03 10:18:28 INFO - [Timer] 立即执行任务: uid=RJRGh1X9IWCo
2025-08-03 10:18:28 INFO - [Timer] Timer initialization completed
2025-08-03 10:18:28 INFO - [Timer] Running timer task for profile: RqkaNnsWj6RG
2025-08-03 10:18:28 INFO - [Timer] Running timer task for profile: RO6hk7oF7wdt
2025-08-03 10:18:28 INFO - [Timer] 配置 RO6hk7oF7wdt 是否为当前激活配置: false
2025-08-03 10:18:28 INFO - [Config] [订阅更新] 开始更新订阅 RO6hk7oF7wdt
2025-08-03 10:18:28 INFO - [Timer] Running timer task for profile: RFHm8frTGpzz
2025-08-03 10:18:28 INFO - [Timer] 配置 RFHm8frTGpzz 是否为当前激活配置: false
2025-08-03 10:18:28 INFO - [Config] [订阅更新] 开始更新订阅 RFHm8frTGpzz
2025-08-03 10:18:28 INFO - [订阅更新] RFHm8frTGpzz 是远程订阅，URL: http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae
2025-08-03 10:18:28 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 10:18:28 INFO - [Timer] Running timer task for profile: RKzTK4d5rjIF
2025-08-03 10:18:28 INFO - [Timer] 配置 RKzTK4d5rjIF 是否为当前激活配置: false
2025-08-03 10:18:28 INFO - [Config] [订阅更新] 开始更新订阅 RKzTK4d5rjIF
2025-08-03 10:18:28 INFO - [订阅更新] RKzTK4d5rjIF 是远程订阅，URL: https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797
2025-08-03 10:18:28 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 10:18:28 INFO - [Timer] Running timer task for profile: RqUQUTUdf5hK
2025-08-03 10:18:28 INFO - [Timer] 配置 RqUQUTUdf5hK 是否为当前激活配置: false
2025-08-03 10:18:28 INFO - [Config] [订阅更新] 开始更新订阅 RqUQUTUdf5hK
2025-08-03 10:18:28 INFO - [订阅更新] RqUQUTUdf5hK 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7
2025-08-03 10:18:28 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 10:18:28 INFO - [订阅更新] RO6hk7oF7wdt 是远程订阅，URL: https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372
2025-08-03 10:18:28 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 10:18:28 INFO - [Timer] Running timer task for profile: RJRGh1X9IWCo
2025-08-03 10:18:28 INFO - [Timer] 配置 RqkaNnsWj6RG 是否为当前激活配置: false
2025-08-03 10:18:28 INFO - [Config] [订阅更新] 开始更新订阅 RqkaNnsWj6RG
2025-08-03 10:18:28 INFO - [订阅更新] RqkaNnsWj6RG 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640
2025-08-03 10:18:28 INFO - [Timer] Running timer task for profile: Rx9LL6kjRU3B
2025-08-03 10:18:28 INFO - [Timer] 配置 Rx9LL6kjRU3B 是否为当前激活配置: true
2025-08-03 10:18:28 INFO - [Config] [订阅更新] 开始更新订阅 Rx9LL6kjRU3B
2025-08-03 10:18:28 INFO - [Timer] 配置 RJRGh1X9IWCo 是否为当前激活配置: false
2025-08-03 10:18:28 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 10:18:28 INFO - [订阅更新] Rx9LL6kjRU3B 是远程订阅，URL: https://sub.lbb886.nyc.mn/sub?token=aaabbb
2025-08-03 10:18:28 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 10:18:28 INFO - [Config] [订阅更新] 开始更新订阅 RJRGh1X9IWCo
2025-08-03 10:18:28 INFO - [订阅更新] RJRGh1X9IWCo 是远程订阅，URL: https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7
2025-08-03 10:18:28 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 10:18:28 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)，尝试使用Clash代理更新
2025-08-03 10:18:28 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)，尝试使用Clash代理更新
2025-08-03 10:18:28 INFO - [Frontend] 启动过程中发现错误，加入消息队列: update_retry_with_clash - RqUQUTUdf5hK
2025-08-03 10:18:28 INFO - [Frontend] 启动过程中发现错误，加入消息队列: update_retry_with_clash - RqkaNnsWj6RG
2025-08-03 10:18:29 INFO - [Frontend] 启动过程中发现错误，加入消息队列: config_validate::success - 
2025-08-03 10:18:29 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7)，尝试使用Clash代理更新
2025-08-03 10:18:29 INFO - [Frontend] 启动过程中发现错误，加入消息队列: update_retry_with_clash - RJRGh1X9IWCo
2025-08-03 10:18:29 INFO - [Network] 正在重置所有HTTP客户端
2025-08-03 10:18:29 INFO - [Setup] 异步设置任务完成，耗时: 3.6937615s
2025-08-03 10:18:29 INFO - [Setup] 应用设置成功完成
2025-08-03 10:18:29 INFO - [Frontend] 发送4条启动时累积的错误消息
2025-08-03 10:18:29 INFO - [Window] 窗口已立即显示
2025-08-03 10:18:29 INFO - [Window] 开始监控UI加载状态 (最多8秒)...
2025-08-03 10:18:29 INFO - [Window] 窗口显示流程完成
2025-08-03 10:18:29 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7)
2025-08-03 10:18:29 ERROR - [[Timer]] Failed to update profile uid RJRGh1X9IWCo: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7)
2025-08-03 10:18:29 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 10:18:29 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-03 10:18:29 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-03 10:18:29 ERROR - [[Timer]] Failed to update profile uid RqUQUTUdf5hK: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-03 10:18:29 ERROR - [[Timer]] Failed to update profile uid RqkaNnsWj6RG: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-03 10:18:29 INFO - [订阅更新] 更新订阅配置成功
2025-08-03 10:18:29 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-08-03 10:18:29 INFO - [Timer] Timer task completed successfully for uid: RKzTK4d5rjIF (took 835ms)
2025-08-03 10:18:29 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://sub.lbb886.nyc.mn/sub?token=aaabbb)，尝试使用Clash代理更新
2025-08-03 10:18:29 INFO - [Network] 正在重置所有HTTP客户端
2025-08-03 10:18:29 INFO - UI加载阶段更新: Loading
2025-08-03 10:18:29 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://sub.lbb886.nyc.mn/sub?token=aaabbb)
2025-08-03 10:18:29 ERROR - [[Timer]] Failed to update profile uid Rx9LL6kjRU3B: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://sub.lbb886.nyc.mn/sub?token=aaabbb)
2025-08-03 10:18:29 ERROR - error sending request for url (https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/github-copilot.svg)
2025-08-03 10:18:29 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-03 10:18:30 INFO - UI加载阶段更新: Loading
2025-08-03 10:18:30 INFO - UI加载阶段更新: DomReady
2025-08-03 10:18:30 INFO - UI加载阶段更新: ResourcesLoaded
2025-08-03 10:18:30 INFO - 前端UI已准备就绪
2025-08-03 10:18:30 INFO - [Window] UI已标记为完全就绪
2025-08-03 10:18:30 INFO - [Window] UI已完全加载就绪
2025-08-03 10:18:30 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372)，尝试使用Clash代理更新
2025-08-03 10:18:30 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372)
2025-08-03 10:18:30 ERROR - [[Timer]] Failed to update profile uid RO6hk7oF7wdt: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372)
2025-08-03 10:18:30 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae)，尝试使用Clash代理更新
2025-08-03 10:18:30 INFO - [Network] 正在重置所有HTTP客户端
2025-08-03 10:18:30 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile with status 502 Bad Gateway
2025-08-03 10:18:30 ERROR - [[Timer]] Failed to update profile uid RFHm8frTGpzz: failed to fetch remote profile with status 502 Bad Gateway
2025-08-03 10:18:32 INFO - [Window] 开始创建/显示主窗口, is_show=false
2025-08-03 10:18:32 INFO - [Window] 静默模式启动时不创建窗口
2025-08-03 10:18:32 INFO - [Lightweight] 轻量模式已开启
2025-08-03 10:18:32 INFO - [Setup] 检测到已有应用实例运行
2025-08-03 10:18:32 INFO - [Config] clash_core配置验证通过: Some("verge-mihomo")
2025-08-03 10:18:32 INFO - [Setup] 初始化资源...
2025-08-03 10:18:32 INFO - [Setup] 初始化完成，继续执行
2025-08-03 10:18:32 INFO - [System] 应用就绪或恢复
2025-08-03 10:18:32 INFO - [System] 开始执行清理操作...
2025-08-03 10:18:32 INFO - [System] 开始执行异步清理操作...
2025-08-03 10:18:32 INFO - 核心服务已停止
2025-08-03 10:18:32 INFO - 系统代理已重置
2025-08-03 10:18:32 INFO - [Config] 生成运行时配置成功
2025-08-03 10:18:32 INFO - [Config] 开始验证配置
2025-08-03 10:18:32 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 10:18:32 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 10:18:32 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 10:18:32 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 10:18:32 INFO - TUN模式已禁用
2025-08-03 10:18:32 INFO - [System] 异步清理操作完成 - TUN: true, 代理: true, 核心: true, DNS: true, 总体: true
2025-08-03 10:18:32 INFO - [System] 清理操作完成，结果: true
2025-08-03 10:18:37 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 10:18:54 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 10:18:54 INFO - [Cmd] 开始修改配置文件，请求序列号: 1, 目标profile: Some("ReTBoIS72M3G")
2025-08-03 10:18:54 INFO - [Cmd] 当前配置: Some("Rx9LL6kjRU3B")
2025-08-03 10:18:54 INFO - [Cmd] 正在切换到新配置: ReTBoIS72M3G
2025-08-03 10:18:54 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 10:18:54 INFO - [Cmd] 设置当前处理profile: ReTBoIS72M3G, 序列号: 1
2025-08-03 10:18:54 INFO - [Cmd] 正在更新配置草稿，序列号: 1
2025-08-03 10:18:54 INFO - [Cmd] 开始内核配置更新，序列号: 1
2025-08-03 10:18:54 INFO - [Config] 开始更新配置
2025-08-03 10:18:54 INFO - [Config] 生成新的配置内容
2025-08-03 10:18:54 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 10:18:54 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 10:18:54 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 10:18:54 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 10:18:55 INFO - [Config] -------- 验证结果 --------
2025-08-03 10:18:55 INFO - [Config] 验证成功
2025-08-03 10:18:55 INFO - [Config] -------- 验证结束 --------
2025-08-03 10:18:55 INFO - [Config] 配置验证通过
2025-08-03 10:18:55 INFO - [Config] 生成运行时配置
2025-08-03 10:18:58 INFO - [Core] Configuration updated successfully
2025-08-03 10:18:58 INFO - [Cmd] 配置更新成功，序列号: 1
2025-08-03 10:18:58 INFO - [Cmd] 向前端发送配置变更事件: ReTBoIS72M3G, 序列号: 1
2025-08-03 10:18:58 INFO - [Cmd] 配置切换完成，清理状态，序列号: 1
2025-08-03 10:18:58 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 10:18:58 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 10:18:58 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-03 10:18:58 INFO - [Cmd] 开始修改配置文件，请求序列号: 2, 目标profile: Some("Rx9LL6kjRU3B")
2025-08-03 10:18:58 INFO - [Cmd] 当前配置: Some("ReTBoIS72M3G")
2025-08-03 10:18:58 INFO - [Cmd] 正在切换到新配置: Rx9LL6kjRU3B
2025-08-03 10:18:58 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 10:18:58 INFO - [Cmd] 设置当前处理profile: Rx9LL6kjRU3B, 序列号: 2
2025-08-03 10:18:58 INFO - [Cmd] 正在更新配置草稿，序列号: 2
2025-08-03 10:18:58 INFO - [Cmd] 开始内核配置更新，序列号: 2
2025-08-03 10:18:58 INFO - [Config] 开始更新配置
2025-08-03 10:18:58 INFO - [Config] 生成新的配置内容
2025-08-03 10:18:58 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 10:18:58 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 10:18:58 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 10:18:58 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 10:18:59 INFO - [Config] -------- 验证结果 --------
2025-08-03 10:18:59 INFO - [Config] 验证成功
2025-08-03 10:18:59 INFO - [Config] -------- 验证结束 --------
2025-08-03 10:18:59 INFO - [Config] 配置验证通过
2025-08-03 10:18:59 INFO - [Config] 生成运行时配置
2025-08-03 10:19:01 INFO - [Core] Configuration updated successfully
2025-08-03 10:19:01 INFO - [Cmd] 配置更新成功，序列号: 2
2025-08-03 10:19:01 INFO - [Cmd] 向前端发送配置变更事件: Rx9LL6kjRU3B, 序列号: 2
2025-08-03 10:19:01 INFO - [Cmd] 配置切换完成，清理状态，序列号: 2
2025-08-03 10:19:01 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 10:19:01 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 10:22:05 INFO - Tray点击事件: 显示主窗口
2025-08-03 10:22:05 INFO - 当前在轻量模式，正在退出轻量模式
2025-08-03 10:22:05 INFO - [Lightweight] 轻量模式已关闭
2025-08-03 10:22:05 INFO - [Window] UI就绪状态已重置
2025-08-03 10:22:05 INFO - [防抖] 窗口操作被允许执行
2025-08-03 10:22:05 INFO - [Window] 开始智能显示主窗口
2025-08-03 10:22:05 INFO - [Window] 开始激活窗口
2025-08-03 10:22:05 INFO - [Window] 窗口激活成功
2025-08-03 10:22:05 INFO - 窗口显示结果: Shown
2025-08-03 10:23:01 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 10:23:01 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 10:29:08 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 10:29:08 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 10:29:09 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 10:29:09 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:11:42 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:11:48 INFO - Profile 文件清理完成: 总文件数=81, 删除文件数=0, 失败数=0
2025-08-03 11:11:48 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:11:51 INFO - Profile 文件清理完成: 总文件数=75, 删除文件数=0, 失败数=0
2025-08-03 11:11:51 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:11:54 INFO - Profile 文件清理完成: 总文件数=69, 删除文件数=0, 失败数=0
2025-08-03 11:11:54 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:11:58 INFO - Profile 文件清理完成: 总文件数=63, 删除文件数=0, 失败数=0
2025-08-03 11:11:58 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:12:00 INFO - Profile 文件清理完成: 总文件数=57, 删除文件数=0, 失败数=0
2025-08-03 11:12:00 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:12:00 INFO - [Config] [订阅更新] 开始更新订阅 RFHm8frTGpzz
2025-08-03 11:12:00 INFO - [订阅更新] RFHm8frTGpzz 是远程订阅，URL: http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae
2025-08-03 11:12:00 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:12:02 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile with status 410 Gone，尝试使用Clash代理更新
2025-08-03 11:12:02 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile with status 410 Gone
2025-08-03 11:12:02 ERROR - failed to fetch remote profile with status 410 Gone
2025-08-03 11:14:10 INFO - Profile 文件清理完成: 总文件数=51, 删除文件数=0, 失败数=0
2025-08-03 11:14:10 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:14:16 INFO - [Config] [订阅更新] 开始更新订阅 Rx9LL6kjRU3B
2025-08-03 11:14:16 INFO - [订阅更新] Rx9LL6kjRU3B 是远程订阅，URL: https://sub.lbb886.nyc.mn/sub?token=aaabbb
2025-08-03 11:14:16 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:20 INFO - [Config] [订阅更新] 开始更新订阅 RJRGh1X9IWCo
2025-08-03 11:14:20 INFO - [订阅更新] RJRGh1X9IWCo 是远程订阅，URL: https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7
2025-08-03 11:14:20 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:21 INFO - [订阅更新] 更新订阅配置成功
2025-08-03 11:14:21 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-08-03 11:14:21 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:14:26 INFO - [Config] [订阅更新] 开始更新订阅 ReTBoIS72M3G
2025-08-03 11:14:26 INFO - [订阅更新] ReTBoIS72M3G 是远程订阅，URL: https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1
2025-08-03 11:14:26 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:26 INFO - [Config] [订阅更新] 开始更新订阅 RKzTK4d5rjIF
2025-08-03 11:14:26 INFO - [订阅更新] RKzTK4d5rjIF 是远程订阅，URL: https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797
2025-08-03 11:14:26 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:26 INFO - [Config] [订阅更新] 开始更新订阅 RqUQUTUdf5hK
2025-08-03 11:14:26 INFO - [订阅更新] RqUQUTUdf5hK 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7
2025-08-03 11:14:26 INFO - [Config] [订阅更新] 开始更新订阅 RqkaNnsWj6RG
2025-08-03 11:14:26 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:26 INFO - [Config] [订阅更新] 开始更新订阅 RJRGh1X9IWCo
2025-08-03 11:14:26 INFO - [订阅更新] RJRGh1X9IWCo 是远程订阅，URL: https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7
2025-08-03 11:14:26 INFO - [订阅更新] RqkaNnsWj6RG 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640
2025-08-03 11:14:26 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:26 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:27 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)，尝试使用Clash代理更新
2025-08-03 11:14:27 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)，尝试使用Clash代理更新
2025-08-03 11:14:27 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-03 11:14:27 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-03 11:14:27 ERROR - failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-03 11:14:27 ERROR - failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-03 11:14:27 INFO - [订阅更新] 更新订阅配置成功
2025-08-03 11:14:27 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-08-03 11:14:27 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:14:27 INFO - [订阅更新] 更新订阅配置成功
2025-08-03 11:14:27 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-08-03 11:14:28 INFO - [订阅更新] 更新订阅配置成功
2025-08-03 11:14:28 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-08-03 11:14:29 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:14:32 INFO - [Config] [订阅更新] 开始更新订阅 RqUQUTUdf5hK
2025-08-03 11:14:32 INFO - [订阅更新] RqUQUTUdf5hK 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7
2025-08-03 11:14:32 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:32 INFO - [Network] 正在重置所有HTTP客户端
2025-08-03 11:14:32 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)，尝试使用Clash代理更新
2025-08-03 11:14:33 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-03 11:14:33 ERROR - failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-03 11:14:36 WARN - [Network] 请求超时取消: https://sub.lbb886.nyc.mn/sub?token=aaabbb
2025-08-03 11:14:36 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://sub.lbb886.nyc.mn/sub?token=aaabbb)，尝试使用Clash代理更新
2025-08-03 11:14:36 INFO - [Network] 正在重置所有HTTP客户端
2025-08-03 11:14:38 INFO - [订阅更新] 使用Clash代理更新成功
2025-08-03 11:14:38 INFO - [订阅更新] 是否为当前使用的订阅: true
2025-08-03 11:14:38 INFO - [Config] [订阅更新] 更新内核配置
2025-08-03 11:14:38 INFO - [Config] 开始更新配置
2025-08-03 11:14:38 INFO - [Config] 生成新的配置内容
2025-08-03 11:14:38 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 11:14:38 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 11:14:38 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 11:14:38 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 11:14:39 INFO - [Config] -------- 验证结果 --------
2025-08-03 11:14:39 INFO - [Config] 验证成功
2025-08-03 11:14:39 INFO - [Config] -------- 验证结束 --------
2025-08-03 11:14:39 INFO - [Config] 配置验证通过
2025-08-03 11:14:39 INFO - [Config] 生成运行时配置
2025-08-03 11:14:39 INFO - [Config] [订阅更新] 开始更新订阅 RqkaNnsWj6RG
2025-08-03 11:14:39 INFO - [订阅更新] RqkaNnsWj6RG 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640
2025-08-03 11:14:39 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:40 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)，尝试使用Clash代理更新
2025-08-03 11:14:40 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-03 11:14:40 ERROR - failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-03 11:14:40 INFO - [Core] Configuration updated successfully
2025-08-03 11:14:40 INFO - [Config] [订阅更新] 更新成功
2025-08-03 11:14:40 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:14:45 INFO - [Config] [订阅更新] 开始更新订阅 RqkaNnsWj6RG
2025-08-03 11:14:45 INFO - [订阅更新] RqkaNnsWj6RG 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640
2025-08-03 11:14:45 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:45 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)，尝试使用Clash代理更新
2025-08-03 11:14:45 INFO - [Network] 正在重置所有HTTP客户端
2025-08-03 11:14:45 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-03 11:14:45 ERROR - failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-03 11:14:47 INFO - [Config] [订阅更新] 开始更新订阅 RqUQUTUdf5hK
2025-08-03 11:14:47 INFO - [订阅更新] RqUQUTUdf5hK 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7
2025-08-03 11:14:47 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-03 11:14:47 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)，尝试使用Clash代理更新
2025-08-03 11:14:47 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-03 11:14:47 ERROR - failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-03 11:16:34 INFO - Tray点击事件: 显示主窗口
2025-08-03 11:16:34 INFO - [防抖] 窗口操作被允许执行
2025-08-03 11:16:34 INFO - [Window] 开始智能显示主窗口
2025-08-03 11:16:34 INFO - [Window] 开始激活窗口
2025-08-03 11:16:34 INFO - [Window] 窗口激活成功
2025-08-03 11:16:34 INFO - 窗口显示结果: Shown
2025-08-03 11:16:36 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:16:43 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 11:16:43 INFO - [Timer] Refreshing 2 timer tasks
2025-08-03 11:16:43 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 11:16:50 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 11:16:50 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:00:52 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 12:00:52 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:00:58 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:01:00 INFO - [Cmd] 开始修改配置文件，请求序列号: 3, 目标profile: Some("ReTBoIS72M3G")
2025-08-03 12:01:00 INFO - [Cmd] 当前配置: Some("Rx9LL6kjRU3B")
2025-08-03 12:01:00 INFO - [Cmd] 正在切换到新配置: ReTBoIS72M3G
2025-08-03 12:01:00 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:01:00 INFO - [Cmd] 设置当前处理profile: ReTBoIS72M3G, 序列号: 3
2025-08-03 12:01:00 INFO - [Cmd] 正在更新配置草稿，序列号: 3
2025-08-03 12:01:00 INFO - [Cmd] 开始内核配置更新，序列号: 3
2025-08-03 12:01:00 INFO - [Config] 开始更新配置
2025-08-03 12:01:00 INFO - [Config] 生成新的配置内容
2025-08-03 12:01:00 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:01:00 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:01:00 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:01:00 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:01:01 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:01:01 INFO - [Config] 验证成功
2025-08-03 12:01:01 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:01:01 INFO - [Config] 配置验证通过
2025-08-03 12:01:01 INFO - [Config] 生成运行时配置
2025-08-03 12:01:03 INFO - [Core] Configuration updated successfully
2025-08-03 12:01:03 INFO - [Cmd] 配置更新成功，序列号: 3
2025-08-03 12:01:03 INFO - [Cmd] 向前端发送配置变更事件: ReTBoIS72M3G, 序列号: 3
2025-08-03 12:01:03 INFO - [Cmd] 配置切换完成，清理状态，序列号: 3
2025-08-03 12:01:03 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:01:03 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:01:04 INFO - [Cmd] 开始修改配置文件，请求序列号: 4, 目标profile: Some("Rx9LL6kjRU3B")
2025-08-03 12:01:04 INFO - [Cmd] 当前配置: Some("ReTBoIS72M3G")
2025-08-03 12:01:04 INFO - [Cmd] 正在切换到新配置: Rx9LL6kjRU3B
2025-08-03 12:01:04 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:01:04 INFO - [Cmd] 设置当前处理profile: Rx9LL6kjRU3B, 序列号: 4
2025-08-03 12:01:04 INFO - [Cmd] 正在更新配置草稿，序列号: 4
2025-08-03 12:01:04 INFO - [Cmd] 开始内核配置更新，序列号: 4
2025-08-03 12:01:04 INFO - [Config] 开始更新配置
2025-08-03 12:01:04 INFO - [Config] 生成新的配置内容
2025-08-03 12:01:04 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:01:04 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:01:04 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:01:04 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:01:05 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:01:05 INFO - [Config] 验证成功
2025-08-03 12:01:05 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:01:05 INFO - [Config] 配置验证通过
2025-08-03 12:01:05 INFO - [Config] 生成运行时配置
2025-08-03 12:01:07 INFO - [Core] Configuration updated successfully
2025-08-03 12:01:07 INFO - [Cmd] 配置更新成功，序列号: 4
2025-08-03 12:01:07 INFO - [Cmd] 向前端发送配置变更事件: Rx9LL6kjRU3B, 序列号: 4
2025-08-03 12:01:07 INFO - [Cmd] 配置切换完成，清理状态，序列号: 4
2025-08-03 12:01:07 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:01:07 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:01:22 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 12:01:22 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:01:37 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 12:01:37 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:03:51 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 12:03:51 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:07:45 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:07:46 INFO - [Cmd] 开始修改配置文件，请求序列号: 5, 目标profile: Some("ReTBoIS72M3G")
2025-08-03 12:07:46 INFO - [Cmd] 当前配置: Some("Rx9LL6kjRU3B")
2025-08-03 12:07:46 INFO - [Cmd] 正在切换到新配置: ReTBoIS72M3G
2025-08-03 12:07:46 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:07:46 INFO - [Cmd] 设置当前处理profile: ReTBoIS72M3G, 序列号: 5
2025-08-03 12:07:46 INFO - [Cmd] 正在更新配置草稿，序列号: 5
2025-08-03 12:07:46 INFO - [Cmd] 开始内核配置更新，序列号: 5
2025-08-03 12:07:46 INFO - [Config] 开始更新配置
2025-08-03 12:07:46 INFO - [Config] 生成新的配置内容
2025-08-03 12:07:46 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:07:46 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:07:46 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:07:46 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:07:47 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:07:47 INFO - [Config] 验证成功
2025-08-03 12:07:47 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:07:47 INFO - [Config] 配置验证通过
2025-08-03 12:07:47 INFO - [Config] 生成运行时配置
2025-08-03 12:07:48 INFO - [Core] Configuration updated successfully
2025-08-03 12:07:48 INFO - [Cmd] 配置更新成功，序列号: 5
2025-08-03 12:07:48 INFO - [Cmd] 向前端发送配置变更事件: ReTBoIS72M3G, 序列号: 5
2025-08-03 12:07:48 INFO - [Cmd] 配置切换完成，清理状态，序列号: 5
2025-08-03 12:07:48 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:07:48 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:07:49 INFO - [Cmd] 开始修改配置文件，请求序列号: 6, 目标profile: Some("Rx9LL6kjRU3B")
2025-08-03 12:07:49 INFO - [Cmd] 当前配置: Some("ReTBoIS72M3G")
2025-08-03 12:07:49 INFO - [Cmd] 正在切换到新配置: Rx9LL6kjRU3B
2025-08-03 12:07:49 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:07:49 INFO - [Cmd] 设置当前处理profile: Rx9LL6kjRU3B, 序列号: 6
2025-08-03 12:07:49 INFO - [Cmd] 正在更新配置草稿，序列号: 6
2025-08-03 12:07:49 INFO - [Cmd] 开始内核配置更新，序列号: 6
2025-08-03 12:07:49 INFO - [Config] 开始更新配置
2025-08-03 12:07:49 INFO - [Config] 生成新的配置内容
2025-08-03 12:07:49 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:07:49 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:07:49 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:07:49 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:07:50 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:07:50 INFO - [Config] 验证成功
2025-08-03 12:07:50 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:07:50 INFO - [Config] 配置验证通过
2025-08-03 12:07:50 INFO - [Config] 生成运行时配置
2025-08-03 12:07:52 INFO - [Core] Configuration updated successfully
2025-08-03 12:07:52 INFO - [Cmd] 配置更新成功，序列号: 6
2025-08-03 12:07:52 INFO - [Cmd] 向前端发送配置变更事件: Rx9LL6kjRU3B, 序列号: 6
2025-08-03 12:07:52 INFO - [Cmd] 配置切换完成，清理状态，序列号: 6
2025-08-03 12:07:52 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:07:52 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:14:07 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:14:08 INFO - [Cmd] 开始修改配置文件，请求序列号: 7, 目标profile: Some("ReTBoIS72M3G")
2025-08-03 12:14:08 INFO - [Cmd] 当前配置: Some("Rx9LL6kjRU3B")
2025-08-03 12:14:08 INFO - [Cmd] 正在切换到新配置: ReTBoIS72M3G
2025-08-03 12:14:08 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:14:08 INFO - [Cmd] 设置当前处理profile: ReTBoIS72M3G, 序列号: 7
2025-08-03 12:14:08 INFO - [Cmd] 正在更新配置草稿，序列号: 7
2025-08-03 12:14:08 INFO - [Cmd] 开始内核配置更新，序列号: 7
2025-08-03 12:14:08 INFO - [Config] 开始更新配置
2025-08-03 12:14:08 INFO - [Config] 生成新的配置内容
2025-08-03 12:14:08 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:14:08 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:14:08 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:14:08 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:14:09 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:14:09 INFO - [Config] 验证成功
2025-08-03 12:14:09 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:14:09 INFO - [Config] 配置验证通过
2025-08-03 12:14:09 INFO - [Config] 生成运行时配置
2025-08-03 12:14:10 INFO - [Core] Configuration updated successfully
2025-08-03 12:14:10 INFO - [Cmd] 配置更新成功，序列号: 7
2025-08-03 12:14:10 INFO - [Cmd] 向前端发送配置变更事件: ReTBoIS72M3G, 序列号: 7
2025-08-03 12:14:10 INFO - [Cmd] 配置切换完成，清理状态，序列号: 7
2025-08-03 12:14:10 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:14:10 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:14:11 INFO - [Cmd] 开始修改配置文件，请求序列号: 8, 目标profile: Some("Rx9LL6kjRU3B")
2025-08-03 12:14:11 INFO - [Cmd] 当前配置: Some("ReTBoIS72M3G")
2025-08-03 12:14:11 INFO - [Cmd] 正在切换到新配置: Rx9LL6kjRU3B
2025-08-03 12:14:11 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:14:11 INFO - [Cmd] 设置当前处理profile: Rx9LL6kjRU3B, 序列号: 8
2025-08-03 12:14:11 INFO - [Cmd] 正在更新配置草稿，序列号: 8
2025-08-03 12:14:11 INFO - [Cmd] 开始内核配置更新，序列号: 8
2025-08-03 12:14:11 INFO - [Config] 开始更新配置
2025-08-03 12:14:11 INFO - [Config] 生成新的配置内容
2025-08-03 12:14:11 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:14:11 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:14:11 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:14:11 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:14:12 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:14:12 INFO - [Config] 验证成功
2025-08-03 12:14:12 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:14:12 INFO - [Config] 配置验证通过
2025-08-03 12:14:12 INFO - [Config] 生成运行时配置
2025-08-03 12:14:14 INFO - [Core] Configuration updated successfully
2025-08-03 12:14:14 INFO - [Cmd] 配置更新成功，序列号: 8
2025-08-03 12:14:14 INFO - [Cmd] 向前端发送配置变更事件: Rx9LL6kjRU3B, 序列号: 8
2025-08-03 12:14:14 INFO - [Cmd] 配置切换完成，清理状态，序列号: 8
2025-08-03 12:14:14 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:14:14 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:14:26 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 12:14:26 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:15:13 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:15:14 INFO - [Cmd] 开始修改配置文件，请求序列号: 9, 目标profile: Some("ReTBoIS72M3G")
2025-08-03 12:15:14 INFO - [Cmd] 当前配置: Some("Rx9LL6kjRU3B")
2025-08-03 12:15:14 INFO - [Cmd] 正在切换到新配置: ReTBoIS72M3G
2025-08-03 12:15:14 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:15:14 INFO - [Cmd] 设置当前处理profile: ReTBoIS72M3G, 序列号: 9
2025-08-03 12:15:14 INFO - [Cmd] 正在更新配置草稿，序列号: 9
2025-08-03 12:15:14 INFO - [Cmd] 开始内核配置更新，序列号: 9
2025-08-03 12:15:14 INFO - [Config] 开始更新配置
2025-08-03 12:15:14 INFO - [Config] 生成新的配置内容
2025-08-03 12:15:14 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:15:14 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:15:14 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:15:14 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:15:15 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:15:15 INFO - [Config] 验证成功
2025-08-03 12:15:15 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:15:15 INFO - [Config] 配置验证通过
2025-08-03 12:15:15 INFO - [Config] 生成运行时配置
2025-08-03 12:15:16 INFO - [Core] Configuration updated successfully
2025-08-03 12:15:16 INFO - [Cmd] 配置更新成功，序列号: 9
2025-08-03 12:15:16 INFO - [Cmd] 向前端发送配置变更事件: ReTBoIS72M3G, 序列号: 9
2025-08-03 12:15:16 INFO - [Cmd] 配置切换完成，清理状态，序列号: 9
2025-08-03 12:15:16 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:15:16 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:15:17 INFO - [Cmd] 开始修改配置文件，请求序列号: 10, 目标profile: Some("Rx9LL6kjRU3B")
2025-08-03 12:15:17 INFO - [Cmd] 当前配置: Some("ReTBoIS72M3G")
2025-08-03 12:15:17 INFO - [Cmd] 正在切换到新配置: Rx9LL6kjRU3B
2025-08-03 12:15:17 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:15:17 INFO - [Cmd] 设置当前处理profile: Rx9LL6kjRU3B, 序列号: 10
2025-08-03 12:15:17 INFO - [Cmd] 正在更新配置草稿，序列号: 10
2025-08-03 12:15:17 INFO - [Cmd] 开始内核配置更新，序列号: 10
2025-08-03 12:15:17 INFO - [Config] 开始更新配置
2025-08-03 12:15:17 INFO - [Config] 生成新的配置内容
2025-08-03 12:15:17 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:15:17 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:15:17 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:15:17 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:15:19 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:15:19 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:15:19 INFO - [Config] 验证成功
2025-08-03 12:15:19 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:15:19 INFO - [Config] 配置验证通过
2025-08-03 12:15:19 INFO - [Config] 生成运行时配置
2025-08-03 12:15:19 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-03 12:15:20 INFO - [Core] Configuration updated successfully
2025-08-03 12:15:20 INFO - [Cmd] 配置更新成功，序列号: 10
2025-08-03 12:15:20 INFO - [Cmd] 向前端发送配置变更事件: Rx9LL6kjRU3B, 序列号: 10
2025-08-03 12:15:20 INFO - [Cmd] 配置切换完成，清理状态，序列号: 10
2025-08-03 12:40:01 INFO - Tray点击事件: 显示主窗口
2025-08-03 12:40:01 INFO - [防抖] 窗口操作被允许执行
2025-08-03 12:40:01 INFO - [Window] 开始智能显示主窗口
2025-08-03 12:40:01 INFO - [Window] 开始激活窗口
2025-08-03 12:40:01 INFO - [Window] 窗口激活成功
2025-08-03 12:40:01 INFO - 窗口显示结果: Shown
2025-08-03 12:40:04 INFO - [Service] 开始检查服务是否正在运行
2025-08-03 12:40:04 INFO - [Service] 开始检查服务状态 (IPC)
2025-08-03 12:40:04 INFO - [Service] 正在连接服务 (Windows)...
2025-08-03 12:40:04 INFO - [Service] 服务连接成功 (Windows)
2025-08-03 12:40:04 INFO - [Service] IPC请求完成: 命令=GetClash, 成功=true
2025-08-03 12:40:04 INFO - [Service] 服务检测成功: code=0, msg=ok, data存在=true
2025-08-03 12:40:04 INFO - [Service] 服务正在运行
2025-08-03 12:54:47 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:54:47 INFO - [Cmd] 开始修改配置文件，请求序列号: 11, 目标profile: Some("ReTBoIS72M3G")
2025-08-03 12:54:47 INFO - [Cmd] 当前配置: Some("Rx9LL6kjRU3B")
2025-08-03 12:54:47 INFO - [Cmd] 正在切换到新配置: ReTBoIS72M3G
2025-08-03 12:54:47 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:54:47 INFO - [Cmd] 设置当前处理profile: ReTBoIS72M3G, 序列号: 11
2025-08-03 12:54:47 INFO - [Cmd] 正在更新配置草稿，序列号: 11
2025-08-03 12:54:47 INFO - [Cmd] 开始内核配置更新，序列号: 11
2025-08-03 12:54:47 INFO - [Config] 开始更新配置
2025-08-03 12:54:47 INFO - [Config] 生成新的配置内容
2025-08-03 12:54:47 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:54:47 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:54:47 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:54:47 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:54:49 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:54:49 INFO - [Config] 验证成功
2025-08-03 12:54:49 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:54:49 INFO - [Config] 配置验证通过
2025-08-03 12:54:49 INFO - [Config] 生成运行时配置
2025-08-03 12:54:50 INFO - [Core] Configuration updated successfully
2025-08-03 12:54:50 INFO - [Cmd] 配置更新成功，序列号: 11
2025-08-03 12:54:50 INFO - [Cmd] 向前端发送配置变更事件: ReTBoIS72M3G, 序列号: 11
2025-08-03 12:54:50 INFO - [Cmd] 配置切换完成，清理状态，序列号: 11
2025-08-03 12:54:50 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:54:50 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:54:50 INFO - [Cmd] 开始修改配置文件，请求序列号: 12, 目标profile: Some("Rx9LL6kjRU3B")
2025-08-03 12:54:50 INFO - [Cmd] 当前配置: Some("ReTBoIS72M3G")
2025-08-03 12:54:50 INFO - [Cmd] 正在切换到新配置: Rx9LL6kjRU3B
2025-08-03 12:54:50 INFO - [Cmd] 目标配置文件语法正确
2025-08-03 12:54:50 INFO - [Cmd] 设置当前处理profile: Rx9LL6kjRU3B, 序列号: 12
2025-08-03 12:54:50 INFO - [Cmd] 正在更新配置草稿，序列号: 12
2025-08-03 12:54:50 INFO - [Cmd] 开始内核配置更新，序列号: 12
2025-08-03 12:54:50 INFO - [Config] 开始更新配置
2025-08-03 12:54:50 INFO - [Config] 生成新的配置内容
2025-08-03 12:54:50 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 12:54:50 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 12:54:50 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 12:54:50 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 12:54:51 INFO - [Config] -------- 验证结果 --------
2025-08-03 12:54:51 INFO - [Config] 验证成功
2025-08-03 12:54:51 INFO - [Config] -------- 验证结束 --------
2025-08-03 12:54:51 INFO - [Config] 配置验证通过
2025-08-03 12:54:51 INFO - [Config] 生成运行时配置
2025-08-03 12:54:53 INFO - [Core] Configuration updated successfully
2025-08-03 12:54:53 INFO - [Cmd] 配置更新成功，序列号: 12
2025-08-03 12:54:53 INFO - [Cmd] 向前端发送配置变更事件: Rx9LL6kjRU3B, 序列号: 12
2025-08-03 12:54:53 INFO - [Cmd] 配置切换完成，清理状态，序列号: 12
2025-08-03 12:54:53 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 12:54:53 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 14:03:15 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 14:03:29 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 14:03:34 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 14:03:34 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 14:04:38 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 14:04:38 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 14:04:44 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 14:04:59 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 14:05:02 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 15:33:32 INFO - [Config] 开始更新配置
2025-08-03 15:33:32 INFO - [Config] 生成新的配置内容
2025-08-03 15:33:32 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 15:33:32 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 15:33:32 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 15:33:32 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 15:33:34 INFO - [Config] -------- 验证结果 --------
2025-08-03 15:33:34 INFO - [Config] 验证成功
2025-08-03 15:33:34 INFO - [Config] -------- 验证结束 --------
2025-08-03 15:33:34 INFO - [Config] 配置验证通过
2025-08-03 15:33:34 INFO - [Config] 生成运行时配置
2025-08-03 15:33:35 INFO - [Core] Configuration updated successfully
2025-08-03 16:15:36 INFO - Tray点击事件: 显示主窗口
2025-08-03 16:15:36 INFO - [防抖] 窗口操作被允许执行
2025-08-03 16:15:36 INFO - [Window] 开始智能显示主窗口
2025-08-03 16:15:36 INFO - [Window] 开始激活窗口
2025-08-03 16:15:36 INFO - [Window] 窗口激活成功
2025-08-03 16:15:36 INFO - 窗口显示结果: Shown
2025-08-03 16:15:37 INFO - [Config] 开始更新配置
2025-08-03 16:15:37 INFO - [Config] 生成新的配置内容
2025-08-03 16:15:37 INFO - [Config] 生成临时配置文件用于验证
2025-08-03 16:15:37 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-03 16:15:37 INFO - [Config] 使用内核: verge-mihomo
2025-08-03 16:15:37 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-03 16:15:39 INFO - [Config] -------- 验证结果 --------
2025-08-03 16:15:39 INFO - [Config] 验证成功
2025-08-03 16:15:39 INFO - [Config] -------- 验证结束 --------
2025-08-03 16:15:39 INFO - [Config] 配置验证通过
2025-08-03 16:15:39 INFO - [Config] 生成运行时配置
2025-08-03 16:15:40 INFO - Tray点击事件: 显示主窗口
2025-08-03 16:15:40 INFO - [防抖] 窗口操作被允许执行
2025-08-03 16:15:40 INFO - [Window] 开始智能显示主窗口
2025-08-03 16:15:40 INFO - [Window] 开始激活窗口
2025-08-03 16:15:41 INFO - [Window] 窗口激活成功
2025-08-03 16:15:41 INFO - 窗口显示结果: Shown
2025-08-03 16:15:41 INFO - [Core] Configuration updated successfully
2025-08-03 16:21:58 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 19:23:00 INFO - Tray点击事件: 显示主窗口
2025-08-03 19:23:00 INFO - [防抖] 窗口操作被允许执行
2025-08-03 19:23:00 INFO - [Window] 开始智能显示主窗口
2025-08-03 19:23:00 INFO - [Window] 开始激活窗口
2025-08-03 19:23:00 INFO - [Window] 窗口激活成功
2025-08-03 19:23:00 INFO - 窗口显示结果: Shown
2025-08-03 19:23:05 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 19:30:41 INFO - Tray点击事件: 显示主窗口
2025-08-03 19:30:41 INFO - [防抖] 窗口操作被允许执行
2025-08-03 19:30:41 INFO - [Window] 开始智能显示主窗口
2025-08-03 19:30:41 INFO - [Window] 开始激活窗口
2025-08-03 19:30:41 INFO - [Window] 窗口激活成功
2025-08-03 19:30:41 INFO - 窗口显示结果: Shown
2025-08-03 19:30:43 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 19:30:43 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 20:27:04 INFO - Tray点击事件: 显示主窗口
2025-08-03 20:27:04 INFO - [防抖] 窗口操作被允许执行
2025-08-03 20:27:04 INFO - [Window] 开始智能显示主窗口
2025-08-03 20:27:04 INFO - [Window] 开始激活窗口
2025-08-03 20:27:04 INFO - [Window] 窗口已最小化，正在取消最小化
2025-08-03 20:27:05 INFO - [Window] 窗口激活成功
2025-08-03 20:27:05 INFO - 窗口显示结果: Shown
2025-08-03 20:27:19 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 20:27:24 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 20:27:34 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 20:27:34 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 20:27:36 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 20:27:36 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 20:27:36 ERROR - error sending request for url (https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/github-copilot.svg)
2025-08-03 21:18:59 INFO - Tray点击事件: 显示主窗口
2025-08-03 21:18:59 INFO - [防抖] 窗口操作被允许执行
2025-08-03 21:18:59 INFO - [Window] 开始智能显示主窗口
2025-08-03 21:18:59 INFO - [Window] 开始激活窗口
2025-08-03 21:18:59 INFO - [Window] 窗口激活成功
2025-08-03 21:18:59 INFO - 窗口显示结果: Shown
2025-08-03 21:19:17 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-03 21:19:18 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-03 21:19:24 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-03 21:19:24 INFO - [Cmd] 快速获取配置列表成功
2025-08-03 21:37:18 INFO - Tray点击事件: 显示主窗口
2025-08-03 21:37:18 INFO - [防抖] 窗口操作被允许执行
2025-08-03 21:37:18 INFO - [Window] 开始智能显示主窗口
2025-08-03 21:37:18 INFO - [Window] 开始激活窗口
2025-08-03 21:37:18 INFO - [Window] 窗口激活成功
2025-08-03 21:37:18 INFO - 窗口显示结果: Shown
2025-08-03 21:37:27 INFO - [Service] 通过服务停止核心 (IPC)
2025-08-03 21:37:27 INFO - [Service] 正在连接服务 (Windows)...
2025-08-03 21:37:27 INFO - [Service] 服务连接成功 (Windows)
2025-08-03 21:37:28 INFO - [Service] IPC请求完成: 命令=StopClash, 成功=true
