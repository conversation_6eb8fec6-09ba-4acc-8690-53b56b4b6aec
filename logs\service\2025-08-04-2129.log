Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-08-04T21:29:18.267363700+08:00" level=info msg="Start initial configuration in progress"
time="2025-08-04T21:29:18.273080000+08:00" level=info msg="Geodata Loader mode: standard"
time="2025-08-04T21:29:18.273080000+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-08-04T21:29:18.273607100+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-08-04T21:29:18.277721200+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7482"
time="2025-08-04T21:29:18.277721200+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-08-04T21:29:18.487910400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118239"
time="2025-08-04T21:29:18.495157600+08:00" level=info msg="Initial configuration complete, total time: 225ms"
time="2025-08-04T21:29:18.538091900+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"
time="2025-08-04T21:29:19.190642000+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-04T21:29:19.455072200+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-04T21:29:19.455583100+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"
time="2025-08-04T21:29:19.455583100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"
time="2025-08-04T21:29:19.822266000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T21:29:19.822266000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T21:29:19.822266000+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T21:29:19.822266000+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T21:29:59.822425500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T21:29:59.822425500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T21:31:19.823065400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T21:31:19.823065400+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T21:33:59.823326300+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T21:33:59.823326300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T21:39:19.823717100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T21:39:19.823717100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T21:49:59.824128400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T21:49:59.824128400+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T22:11:19.824368200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T22:11:19.824368200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T22:46:56.542226100+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-04T22:46:56.545406600+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-04T22:53:59.824577600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"
time="2025-08-04T22:53:59.824577600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"
time="2025-08-04T22:56:11.148707000+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-04T22:56:11.151254200+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-04T22:56:14.805876100+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-04T22:56:14.809614300+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-04T22:57:19.773580800+08:00" level=warning msg="Load GeoIP rule: lan"
time="2025-08-04T22:57:19.777910100+08:00" level=warning msg="Load GeoIP rule: lan"
