2025-08-04 19:15:57 INFO - [Config] clash_core配置验证通过: Some("verge-mihomo")
2025-08-04 19:15:57 INFO - [Setup] 初始化资源...
2025-08-04 19:15:57 INFO - [Setup] 初始化完成，继续执行
2025-08-04 19:15:57 INFO - [System] 应用就绪或恢复
2025-08-04 19:15:57 INFO - [Config] 生成运行时配置成功
2025-08-04 19:15:57 INFO - [Config] 开始验证配置
2025-08-04 19:15:57 INFO - [Config] 生成临时配置文件用于验证
2025-08-04 19:15:57 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-04 19:15:57 INFO - [Config] 使用内核: verge-mihomo
2025-08-04 19:15:57 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-04 19:15:57 INFO - [Config] -------- 验证结果 --------
2025-08-04 19:15:57 INFO - [Config] 验证成功
2025-08-04 19:15:57 INFO - [Config] -------- 验证结束 --------
2025-08-04 19:15:57 INFO - [Config] 配置验证成功
2025-08-04 19:15:57 INFO - [Setup] 清理冗余的Profile文件...
2025-08-04 19:15:57 INFO - Profile 文件清理完成: 总文件数=51, 删除文件数=0, 失败数=0
2025-08-04 19:15:57 INFO - [Setup] 启动时Profile文件清理完成
2025-08-04 19:15:57 INFO - [Core] 开始清理多余的 mihomo 进程
2025-08-04 19:15:57 INFO - [Service] 开始检查服务是否正在运行
2025-08-04 19:15:57 INFO - [Service] 开始检查服务状态 (IPC)
2025-08-04 19:15:57 INFO - [Service] 正在连接服务 (Windows)...
2025-08-04 19:15:57 ERROR - [Service] 连接到服务命名管道失败: 系统找不到指定的文件。 (os error 2)
2025-08-04 19:15:57 ERROR - [Service] IPC通信失败: 无法连接到服务命名管道: 系统找不到指定的文件。 (os error 2)
2025-08-04 19:15:57 ERROR - [Service] 检查服务运行状态失败: 无法连接到Clash Verge Service: 无法连接到服务命名管道: 系统找不到指定的文件。 (os error 2)
2025-08-04 19:15:57 INFO - [Core] 服务初始不可用 (is_service_available 调用失败)
2025-08-04 19:15:57 INFO - [Core] 核心未通过服务模式启动，执行Sidecar回退或首次安装逻辑
2025-08-04 19:15:57 INFO - [Core] 有服务安装记录但服务不可用/未启动，强制切换到Sidecar模式
2025-08-04 19:15:57 WARN - [Core] prefer_sidecar 为 false，因服务启动失败或不可用而强制设置为 true
2025-08-04 19:15:57 INFO - [Tray] 创建系统托盘...
2025-08-04 19:15:57 INFO - 正在从AppHandle创建系统托盘
2025-08-04 19:15:57 INFO - 系统托盘创建成功
2025-08-04 19:15:57 INFO - [Tray] 系统托盘创建成功
2025-08-04 19:15:57 INFO - 已启用事件驱动代理守卫
2025-08-04 19:15:57 INFO - [Window] 开始创建/显示主窗口, is_show=true
2025-08-04 19:15:57 INFO - 事件驱动代理管理器启动
2025-08-04 19:15:57 INFO - 初始化代理状态
2025-08-04 19:15:57 INFO - 代理状态初始化完成: sys=false, pac=false
2025-08-04 19:15:57 INFO - [Timer] Initializing timer...
2025-08-04 19:15:57 INFO - [Timer] Refreshing 5 timer tasks
2025-08-04 19:15:57 INFO - [Timer] Adding task: uid=RKzTK4d5rjIF, id=1, interval=1440min
2025-08-04 19:15:57 INFO - [Timer] Adding task: uid=RqkaNnsWj6RG, id=2, interval=1440min
2025-08-04 19:15:57 INFO - [Timer] Adding task: uid=Rx9LL6kjRU3B, id=3, interval=360min
2025-08-04 19:15:57 INFO - [Timer] Adding task: uid=RJRGh1X9IWCo, id=4, interval=1440min
2025-08-04 19:15:57 INFO - [Timer] Adding task: uid=RqUQUTUdf5hK, id=5, interval=1440min
2025-08-04 19:15:57 INFO - [Timer] 已注册的定时任务数量: 5
2025-08-04 19:15:57 INFO - [Timer] 注册了定时任务 - uid=RqkaNnsWj6RG, interval=1440min, task_id=2
2025-08-04 19:15:57 INFO - [Timer] 注册了定时任务 - uid=RJRGh1X9IWCo, interval=1440min, task_id=4
2025-08-04 19:15:57 INFO - [Timer] 注册了定时任务 - uid=RKzTK4d5rjIF, interval=1440min, task_id=1
2025-08-04 19:15:57 INFO - [Timer] 注册了定时任务 - uid=RqUQUTUdf5hK, interval=1440min, task_id=5
2025-08-04 19:15:57 INFO - [Timer] 注册了定时任务 - uid=Rx9LL6kjRU3B, interval=360min, task_id=3
2025-08-04 19:15:57 INFO - [Timer] 需要立即更新的配置: uid=RKzTK4d5rjIF
2025-08-04 19:15:57 INFO - [Timer] 需要立即更新的配置: uid=RqUQUTUdf5hK
2025-08-04 19:15:57 INFO - [Timer] 需要立即更新的配置: uid=RqkaNnsWj6RG
2025-08-04 19:15:57 INFO - [Timer] 需要立即更新的配置: uid=Rx9LL6kjRU3B
2025-08-04 19:15:57 INFO - [Timer] 需要立即更新的配置: uid=RJRGh1X9IWCo
2025-08-04 19:15:57 INFO - [Timer] 需要立即更新的配置数量: 5
2025-08-04 19:15:57 INFO - [Timer] 立即执行任务: uid=RKzTK4d5rjIF
2025-08-04 19:15:57 INFO - [Timer] 立即执行任务: uid=RqUQUTUdf5hK
2025-08-04 19:15:57 INFO - [Timer] 立即执行任务: uid=RqkaNnsWj6RG
2025-08-04 19:15:57 INFO - [Timer] 立即执行任务: uid=Rx9LL6kjRU3B
2025-08-04 19:15:57 INFO - [Timer] 立即执行任务: uid=RJRGh1X9IWCo
2025-08-04 19:15:57 INFO - [Timer] Timer initialization completed
2025-08-04 19:15:57 INFO - [Timer] Running timer task for profile: RJRGh1X9IWCo
2025-08-04 19:15:57 INFO - [Timer] Running timer task for profile: RqUQUTUdf5hK
2025-08-04 19:15:57 INFO - [Timer] Running timer task for profile: RKzTK4d5rjIF
2025-08-04 19:15:57 INFO - [Timer] 配置 RKzTK4d5rjIF 是否为当前激活配置: false
2025-08-04 19:15:57 INFO - [Config] [订阅更新] 开始更新订阅 RKzTK4d5rjIF
2025-08-04 19:15:57 INFO - [Timer] 配置 RJRGh1X9IWCo 是否为当前激活配置: false
2025-08-04 19:15:57 INFO - [Timer] 配置 RqUQUTUdf5hK 是否为当前激活配置: false
2025-08-04 19:15:57 INFO - [Config] [订阅更新] 开始更新订阅 RqUQUTUdf5hK
2025-08-04 19:15:57 INFO - [订阅更新] RqUQUTUdf5hK 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7
2025-08-04 19:15:57 INFO - [Timer] Running timer task for profile: Rx9LL6kjRU3B
2025-08-04 19:15:57 INFO - [Config] [订阅更新] 开始更新订阅 RJRGh1X9IWCo
2025-08-04 19:15:57 INFO - [订阅更新] RJRGh1X9IWCo 是远程订阅，URL: https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7
2025-08-04 19:15:57 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-04 19:15:57 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-04 19:15:57 INFO - [Timer] 配置 Rx9LL6kjRU3B 是否为当前激活配置: true
2025-08-04 19:15:57 INFO - [Config] [订阅更新] 开始更新订阅 Rx9LL6kjRU3B
2025-08-04 19:15:57 INFO - [订阅更新] RKzTK4d5rjIF 是远程订阅，URL: https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797
2025-08-04 19:15:57 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-04 19:15:57 INFO - [订阅更新] Rx9LL6kjRU3B 是远程订阅，URL: https://sub.lbb886.nyc.mn/sub?token=aaabbb
2025-08-04 19:15:57 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-04 19:15:57 INFO - [Timer] Running timer task for profile: RqkaNnsWj6RG
2025-08-04 19:15:57 INFO - [Timer] 配置 RqkaNnsWj6RG 是否为当前激活配置: false
2025-08-04 19:15:57 INFO - [Config] [订阅更新] 开始更新订阅 RqkaNnsWj6RG
2025-08-04 19:15:57 INFO - [订阅更新] RqkaNnsWj6RG 是远程订阅，URL: https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640
2025-08-04 19:15:57 INFO - [订阅更新] 开始下载新的订阅内容
2025-08-04 19:15:57 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)，尝试使用Clash代理更新
2025-08-04 19:15:57 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)，尝试使用Clash代理更新
2025-08-04 19:15:57 INFO - [Frontend] 启动过程中发现错误，加入消息队列: update_retry_with_clash - RqUQUTUdf5hK
2025-08-04 19:15:57 INFO - [Frontend] 启动过程中发现错误，加入消息队列: update_retry_with_clash - RqkaNnsWj6RG
2025-08-04 19:15:58 INFO - [Setup] 异步设置任务完成，耗时: 1.0551394s
2025-08-04 19:15:58 INFO - [Setup] 应用设置成功完成
2025-08-04 19:15:58 INFO - [Frontend] 发送2条启动时累积的错误消息
2025-08-04 19:15:58 INFO - [Window] 窗口已立即显示
2025-08-04 19:15:58 INFO - [Window] 开始监控UI加载状态 (最多8秒)...
2025-08-04 19:15:58 INFO - [Window] 窗口显示流程完成
2025-08-04 19:15:58 INFO - [订阅更新] 更新订阅配置成功
2025-08-04 19:15:58 INFO - [订阅更新] 是否为当前使用的订阅: false
2025-08-04 19:15:58 INFO - [Timer] Timer task completed successfully for uid: RKzTK4d5rjIF (took 568ms)
2025-08-04 19:15:58 INFO - [Cmd] 快速获取配置列表成功
2025-08-04 19:15:58 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7)，尝试使用Clash代理更新
2025-08-04 19:15:58 INFO - [Network] 正在重置所有HTTP客户端
2025-08-04 19:15:58 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-04 19:15:58 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-04 19:15:58 ERROR - [[Timer]] Failed to update profile uid RqUQUTUdf5hK: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7)
2025-08-04 19:15:58 ERROR - [[Timer]] Failed to update profile uid RqkaNnsWj6RG: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640)
2025-08-04 19:15:58 INFO - UI加载阶段更新: Loading
2025-08-04 19:15:58 ERROR - [订阅更新] 使用Clash代理更新仍然失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7)
2025-08-04 19:15:58 ERROR - [[Timer]] Failed to update profile uid RJRGh1X9IWCo: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://bbb.flylink.cyou/api/v1/client/subscribe?token=fa2bcd0859af0432f59662f83fc1a9f7)
2025-08-04 19:15:58 INFO - UI加载阶段更新: Loading
2025-08-04 19:15:58 INFO - UI加载阶段更新: DomReady
2025-08-04 19:15:58 INFO - UI加载阶段更新: ResourcesLoaded
2025-08-04 19:15:58 INFO - 前端UI已准备就绪
2025-08-04 19:15:58 INFO - [Window] UI已标记为完全就绪
2025-08-04 19:15:58 INFO - [Window] UI已完全加载就绪
2025-08-04 19:16:17 WARN - [订阅更新] 正常更新失败: failed to fetch remote profile: Failed to send HTTP request: error sending request for url (https://sub.lbb886.nyc.mn/sub?token=aaabbb)，尝试使用Clash代理更新
2025-08-04 19:16:19 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-04 19:16:19 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-04 19:16:21 INFO - [订阅更新] 使用Clash代理更新成功
2025-08-04 19:16:21 INFO - [订阅更新] 是否为当前使用的订阅: true
2025-08-04 19:16:21 INFO - [Config] [订阅更新] 更新内核配置
2025-08-04 19:16:21 INFO - [Config] 开始更新配置
2025-08-04 19:16:21 INFO - [Config] 生成新的配置内容
2025-08-04 19:16:21 INFO - [Config] 生成临时配置文件用于验证
2025-08-04 19:16:21 INFO - [Config] 开始验证配置文件: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge-check.yaml
2025-08-04 19:16:21 INFO - [Config] 使用内核: verge-mihomo
2025-08-04 19:16:21 INFO - [Config] 验证目录: C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev
2025-08-04 19:16:23 INFO - [Config] -------- 验证结果 --------
2025-08-04 19:16:23 INFO - [Config] 验证成功
2025-08-04 19:16:23 INFO - [Config] -------- 验证结束 --------
2025-08-04 19:16:23 INFO - [Config] 配置验证通过
2025-08-04 19:16:23 INFO - [Config] 生成运行时配置
2025-08-04 19:16:24 INFO - [Core] Configuration updated successfully
2025-08-04 19:16:24 INFO - [Config] [订阅更新] 更新成功
2025-08-04 19:16:24 INFO - [Timer] Timer task completed successfully for uid: Rx9LL6kjRU3B (took 26411ms)
2025-08-04 19:17:19 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-04 19:42:53 INFO - Tray点击事件: 显示主窗口
2025-08-04 19:42:53 INFO - [防抖] 窗口操作被允许执行
2025-08-04 19:42:53 INFO - [Window] 开始智能显示主窗口
2025-08-04 19:42:53 INFO - [Window] 开始激活窗口
2025-08-04 19:42:53 INFO - [Window] 窗口已最小化，正在取消最小化
2025-08-04 19:42:53 INFO - [Window] 窗口激活成功
2025-08-04 19:42:53 INFO - 窗口显示结果: Shown
2025-08-04 19:43:17 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-04 19:43:40 INFO - [Timer] 定时器更新间隔已变更，正在刷新定时器...
2025-08-04 19:43:40 INFO - [Cmd] 快速获取配置列表成功
2025-08-04 19:43:45 INFO - [Cmd] 快速获取配置列表成功
2025-08-04 19:43:52 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-04 19:43:52 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-04 19:43:52 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
2025-08-04 19:43:57 INFO - [Service] 开始检查服务是否正在运行
2025-08-04 19:43:57 INFO - [Service] 开始检查服务状态 (IPC)
2025-08-04 19:43:57 INFO - [Service] 正在连接服务 (Windows)...
2025-08-04 19:43:57 ERROR - [Service] 连接到服务命名管道失败: 系统找不到指定的文件。 (os error 2)
2025-08-04 19:43:57 ERROR - [Service] IPC通信失败: 无法连接到服务命名管道: 系统找不到指定的文件。 (os error 2)
2025-08-04 19:43:57 ERROR - [Service] 检查服务运行状态失败: 无法连接到Clash Verge Service: 无法连接到服务命名管道: 系统找不到指定的文件。 (os error 2)
2025-08-04 19:43:57 INFO - [Core] 服务不可用，根据用户偏好使用Sidecar模式
2025-08-04 19:44:01 ERROR - error sending request for url (https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png)
