time="2025-08-04T19:44:14.787734000+08:00" level=info msg="Start initial configuration in progress"

time="2025-08-04T19:44:14.793374300+08:00" level=info msg="Geodata Loader mode: standard"

time="2025-08-04T19:44:14.793895400+08:00" level=info msg="Geosite Matcher implementation: succinct"

time="2025-08-04T19:44:14.793895400+08:00" level=info msg="Load GeoIP rule: cn"

time="2025-08-04T19:44:14.798014400+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7482"

time="2025-08-04T19:44:14.798014400+08:00" level=info msg="Load GeoSite rule: cn"

time="2025-08-04T19:44:15.003472100+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118239"

time="2025-08-04T19:44:15.008973800+08:00" level=info msg="Initial configuration complete, total time: 218ms"

time="2025-08-04T19:44:15.045333300+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"

time="2025-08-04T19:44:15.047893200+08:00" level=error msg="Start TUN listening error: configure tun interface: Access is denied."

time="2025-08-04T19:44:15.048924400+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:44:15.053577800+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:44:15.053577800+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"

time="2025-08-04T19:44:15.365219100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:44:15.365219100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:44:15.365219100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:44:15.365219100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:44:15.722389100+08:00" level=warning msg="Load GeoIP rule: lan"

time="2025-08-04T19:44:22.284746900+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"

time="2025-08-04T19:44:22.285755900+08:00" level=error msg="Start TUN listening error: configure tun interface: Access is denied."

time="2025-08-04T19:44:22.286800700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:44:22.290434000+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"

time="2025-08-04T19:44:22.290434000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:44:22.633553200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:44:22.633553200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:44:22.633553200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:44:22.633553200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:45:02.634206500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:45:02.634711700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

