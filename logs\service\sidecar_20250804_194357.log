time="2025-08-04T19:43:57.349209400+08:00" level=info msg="Start initial configuration in progress"

time="2025-08-04T19:43:57.354795700+08:00" level=info msg="Geodata Loader mode: standard"

time="2025-08-04T19:43:57.354795700+08:00" level=info msg="Geosite Matcher implementation: succinct"

time="2025-08-04T19:43:57.354795700+08:00" level=info msg="Load GeoIP rule: cn"

time="2025-08-04T19:43:57.362925600+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7482"

time="2025-08-04T19:43:57.362925600+08:00" level=info msg="Load GeoSite rule: cn"

time="2025-08-04T19:43:57.547880200+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118239"

time="2025-08-04T19:43:57.557392600+08:00" level=info msg="Initial configuration complete, total time: 205ms"

time="2025-08-04T19:43:57.593671200+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"

time="2025-08-04T19:43:57.596728500+08:00" level=error msg="Start TUN listening error: configure tun interface: Access is denied."

time="2025-08-04T19:43:57.603423000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:43:57.608646300+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"

time="2025-08-04T19:43:57.608646300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:43:57.809338500+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:43:57.809338500+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:43:57.810338200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:43:57.810338200+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

