time="2025-08-04T19:15:57.576411200+08:00" level=info msg="Start initial configuration in progress"

time="2025-08-04T19:15:57.582544600+08:00" level=info msg="Geodata Loader mode: standard"

time="2025-08-04T19:15:57.582544600+08:00" level=info msg="Geosite Matcher implementation: succinct"

time="2025-08-04T19:15:57.583050200+08:00" level=info msg="Load GeoIP rule: cn"

time="2025-08-04T19:15:57.586682400+08:00" level=info msg="Finished initial GeoIP rule cn => 🔗 直连, records: 7482"

time="2025-08-04T19:15:57.587192200+08:00" level=info msg="Load GeoSite rule: cn"

time="2025-08-04T19:15:57.781541000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 118302"

time="2025-08-04T19:15:57.784952900+08:00" level=info msg="Initial configuration complete, total time: 205ms"

time="2025-08-04T19:15:57.803313400+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"

time="2025-08-04T19:15:57.810734600+08:00" level=error msg="Start TUN listening error: configure tun interface: Access is denied."

time="2025-08-04T19:15:57.813274600+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:15:57.813274600+08:00" level=warning msg="[Provider] creativity-1 not updated for a long time, force refresh"

time="2025-08-04T19:15:57.816413300+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"

time="2025-08-04T19:15:57.816413300+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:15:57.821548000+08:00" level=warning msg="[Provider] cordcloud not updated for a long time, force refresh"

time="2025-08-04T19:15:58.171174400+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: no ip address"

time="2025-08-04T19:15:58.201566100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:15:58.201566100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:15:58.201566100+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:15:58.201566100+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:15:58.400833700+08:00" level=warning msg="Load GeoIP rule: lan"

time="2025-08-04T19:15:58.464785000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:15:58.993840100+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:16:23.078208500+08:00" level=warning msg="[TUN] default interface changed by monitor, => 以太网"

time="2025-08-04T19:16:23.078721400+08:00" level=error msg="Start TUN listening error: configure tun interface: Access is denied."

time="2025-08-04T19:16:23.080780000+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:16:23.092143700+08:00" level=warning msg="[Provider] get subscription-userinfo: &{%!e(string=failed to parse value '')}"

time="2025-08-04T19:16:23.092143700+08:00" level=warning msg="[Provider] 宝可梦-1 not updated for a long time, force refresh"

time="2025-08-04T19:16:23.323912700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:16:23.323912700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:16:23.323912700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:16:23.323912700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:17:03.324095600+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:17:03.324095600+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:18:23.324805700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:18:23.324805700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:21:03.325161700+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:21:03.325161700+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:26:23.326544900+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:26:23.326544900+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

time="2025-08-04T19:37:03.328276200+08:00" level=warning msg="[TCP] dial DIRECT mihomo --> 52pokemon.xz61.cn:443 error: dns resolve failed: couldn't find ip"

time="2025-08-04T19:37:03.328902300+08:00" level=error msg="[Provider] 宝可梦-1 pull error: Get \"https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7\": EOF"

